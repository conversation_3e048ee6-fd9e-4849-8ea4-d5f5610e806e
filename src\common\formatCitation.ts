export function formatCitation(str: string): string {
  const regex1 = /<span class="citation">\$\^\{(\d+)\}\$<\/span>/g
  const regex2 = /<span class="citation">\$\^\{(\d+)\}\$<span>/g
  const regex3 = /<span class="citation">\$\{\^\{(\d+)\}\}\$<\/span>/g

  let result = str
    .replace(regex1, '<span class="citation">$1</span>')
    .replace(regex2, '<span class="citation">$1</span>')
    .replace(regex3, '<span class="citation">$1</span>')
    .replace(/<span class="citation">\$\^\{([\d,]+)\}\$<\/span>/g, (match, digits) => {
      return digits
        .split(',')
        .map((num: string) => `<span class="citation">${num}</span>`)
        .join('')
    })

  result = result.replace(/(#+ [^\n]+)\n(\s*<span class="citation">\d+<\/span>)/g, '$1 $2\n')

  return result
}
