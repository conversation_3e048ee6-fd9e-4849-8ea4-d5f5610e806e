import { onMounted, onUnmounted } from 'vue'

const PluginEventName = 'CelHive-Link-Event'

enum AgentAction {
  QuoteTxt = 'quote-txt',
  ImgPaste = 'img-paste',
  SummaryLink = 'summary-link',
  SelectText = 'select-text',
  TabHTML = 'tab-html',
  CaptureImg = 'capture-img',
}

interface PluginAgentEventProps {
  action: AgentAction
  data: string
}

interface UsePluginAgentProps {
  watchedMessage?: boolean
  onReciveBase64?: (base64: string) => void
}

export const usePluginAgent = ({ watchedMessage, onReciveBase64 }: UsePluginAgentProps = { watchedMessage: false }) => {
  const isCelHiveLink = ref(false)

  const reciveTextFn = new Set<(text: string) => void>()
  const pasteImgFn = new Set<() => void>()
  const summaryLinkFn = new Set<(link: string) => void>()

  const customEvent = function (e: CustomEvent<PluginAgentEventProps>) {
    if (e.detail.action === AgentAction.QuoteTxt) {
      reciveTextFn.forEach(fn => fn(e.detail.data))
    }
    else if (e.detail.action === AgentAction.ImgPaste) {
      pasteImgFn.forEach(fn => fn())
    }
    else if (e.detail.action === AgentAction.SummaryLink) {
      summaryLinkFn.forEach(fn => fn(e.detail.data))
    }
    else if (e.detail.action === AgentAction.SelectText) {
      reciveTextFn.forEach(fn => fn(e.detail.data))
    }
  } as EventListener

  const observerAction = () => {
    isCelHiveLink.value = document.body.getAttribute('data-celhive-link') === 'true'
  }

  const observer = new MutationObserver(observerAction)

  const onQuoteTxt = (callback: (text: string) => void) => {
    reciveTextFn.add(callback)
  }

  const onPasteImgFn = (callback: () => void) => {
    pasteImgFn.add(callback)
  }

  const onSummaryLinkFn = (callback: (link: string) => void) => {
    summaryLinkFn.add(callback)
  }

  const onMessage = (e: MessageEvent<{ action: AgentAction, data: string }>) => {
    if (isCelHiveLink.value === false) {
      return
    }
    if (!watchedMessage) {
      return
    }

    if (e.data.action === AgentAction.SelectText) {
      reciveTextFn.forEach(fn => fn(e.data.data))
    }
    else if (e.data.action === AgentAction.TabHTML) {
      sessionStorage.setItem('celhive-link-html', e.data.data)
    }
    else if (e.data.action === AgentAction.CaptureImg) {
      if (e.data.data.startsWith('data:image/')) {
        onReciveBase64 && onReciveBase64(e.data.data)
      }
    }
  }

  onMounted(() => {
    document.addEventListener(PluginEventName, customEvent)
    observer.observe(document.body, {
      attributes: true,
    })
    observerAction()
    window.addEventListener('message', onMessage)
  })

  onUnmounted(() => {
    reciveTextFn.clear()
    pasteImgFn.clear()
    summaryLinkFn.clear()
    observer.disconnect()
    document.removeEventListener(PluginEventName, customEvent)
    window.removeEventListener('message', onMessage)
  })

  return {
    isCelHiveLink,
    onQuoteTxt,
    onPasteImgFn,
    onSummaryLinkFn,
  }
}
