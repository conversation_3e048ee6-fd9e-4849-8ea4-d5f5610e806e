import fs from 'node:fs/promises'
import path from 'node:path'
import { describe, expect, it } from 'vitest'
import { filterHTML, getDOMByIndex, htmlToIndexedDOM, extractURL } from '../tool'

/**
 *  @vitest-environment jsdom
 */
describe('htmlToIndexedDOM', () => {
  const html = `
    <body>
      <div>
        <p>hello</p>
        <img src="https://example.com/image.jpg" />
        <a href="https://example.com">link</a>
      </div>
    </body>
  `
  const result = htmlToIndexedDOM(html)

  it('should contain body tag', () => {
    const html = '<p>test</p>'
    expect(() => htmlToIndexedDOM(html)).toThrowError()
  })

  it('should parse HTML and return indexed DOM nodes', () => {
    expect(result).toEqual(
      [
        {
          content: 'hello',
          index: '0-0',
          tagName: 'p',
        },
        {
          content: 'link',
          index: '0-2',
          tagName: 'a',
        },
      ],
    )
  })

  it('get DOMByIndex should return the correct DOM node', () => {
    const html = `
       <div>
        <p>hello</p>
        <img src="https://example.com/image.jpg" />
        <a href="https://example.com">link</a>
      </div>
    `
    const body = document.createElement('body')
    body.innerHTML = html
    const beforeBody = document.body
    document.body = body

    const indexMap = htmlToIndexedDOM(body.outerHTML)
    indexMap.forEach((item) => {
      const dom = getDOMByIndex(item.index)
      expect(dom).toBeDefined()
      expect(dom!.textContent).toBe(item.content)
      expect(dom!.tagName.toLowerCase()).toBe(item.tagName)
    })

    document.body = beforeBody
  })
})

describe('filter ht', () => {
  it('should filter html', async () => {
    const html = await fs.readFile(path.join(__dirname, 'weixin.html'), 'utf-8')
    expect(filterHTML(html)).toMatchSnapshot()
  })

  it('should filter html', async () => {
    const html = await fs.readFile(path.join(__dirname, 'weixin01.html'), 'utf-8')
    expect(filterHTML(html)).toMatchSnapshot('MiniCPM 4.0 技术博客')
  })
})

describe('extract url', () => {
  it('should get url from string', () => {
    const str = 'hello world https://www.goldjournal.net/article/S0090-4295(25)00502-3/fulltext https://biying.com https://bcying.com'
    expect(extractURL(str)).toEqual({
      urls:['https://www.goldjournal.net/article/S0090-4295(25)00502-3/fulltext', 'https://biying.com', 'https://bcying.com'],
      extractURL: 'hello world'
    })
  })
})
