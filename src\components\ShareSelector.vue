<template>
  <div>
    <div class="mb-24px">{{ $t('sideBar.shareSelectorDialogDescription') }} </div>
    <div
      class="bg-#FAFAFA dark:bg-#1E1E1E rounded-full leading-48px flex justify-between items-center pl-22px pr-10px border border-#EEEEEE mb-20px">
      <div class="flex-1 truncate">https://www.juchats.com/share ...</div>
      <div @click="onCreate"
        class="px-12px leading-32px flex justify-center items-center gap-5px bg-black rounded-full text-white cursor-pointer">
        <i class="i-ri-link"></i>
        <span>{{ shared ? $t('tipMessage.copyLinkSuccess') : createButtonTxt }}</span>
      </div>
    </div>
    <div class="action">
      <div class="option">
        <svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="Juchats-Dev-Share" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="Website-Index-Select-Share" transform="translate(-912, -665)">
              <g id="Chat" transform="translate(912, 665)">
                <circle id="椭圆形" fill="#F9F9F9" cx="14" cy="14" r="14"></circle>
                <g id="编组-6" transform="translate(7, 9)">
                  <rect id="矩形" fill="#000000" fill-rule="nonzero" x="0" y="6.77419355" width="8.12903226" height="2.7">
                  </rect>
                  <circle id="椭圆形" fill="#FF1616" cx="11.2903226" cy="2.70967742" r="2.70967742"></circle>
                </g>
              </g>
            </g>
          </g>
        </svg>
        <div class="desc">{{ $t('sideBar.shareChat') }}</div>
        <Switch v-model="shareChat" :disabled="disabledChat" />
      </div>
      <div class="option">
        <svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink">
          <g id="Juchats-Dev-Share" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="Website-Index-Select-Share" transform="translate(-912, -709)">
              <g id="Artifacts" transform="translate(912, 709)">
                <circle id="椭圆形" fill="#F9F9F9" cx="14" cy="14" r="14"></circle>
                <g id="编组-7" transform="translate(7, 9)" fill-rule="nonzero">
                  <polygon id="形状" fill="#F43513"
                    points="7.72048018 7.2022068 14 7.2022068 14 10.0728444 7.72048018 10.0728444"></polygon>
                  <polygon id="路径" fill="#000000"
                    points="3.33813435 5.00719563 0 1.66906128 1.66907308 2.2105774e-14 6.67629231 5.00719563 1.66907308 10.0143441 0 8.34529458">
                  </polygon>
                </g>
              </g>
            </g>
          </g>
        </svg>
        <div class="desc">{{ $t('sideBar.shareWebsite') }}</div>
        <Switch v-model="shareArtifact" :disabled="disabledArtifact" />
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import Switch from '@/components/Switch.vue'

const t = useGlobalI18n()

const props = defineProps<{
  /** 对话有没有被分享 */
  shared: boolean
  disabledChat?: boolean
  disabledArtifact?: boolean
  defaultOpenChat?: boolean
  defaultOpenArtifact?: boolean
}>()

const shareChat = ref(props.defaultOpenChat)
const shareArtifact = ref(props.defaultOpenArtifact)
const createButtonTxt = ref(t('sideBar.shareSelectorDialogCreate').value)

const emits = defineEmits<{
  (e: 'create', option: { shareChat: boolean, shareArtifact: boolean }): Promise<void>
  (e: 'change', option: { shareChat: boolean, shareArtifact: boolean }): void
}>()


watch([shareChat, shareArtifact], ([shareChat, shareArtifact]) => {
  emits('change', { shareChat, shareArtifact })
})

const onCreate = async () => {
  console.log('onCreate', shareChat.value, shareArtifact.value)
  await emits('create', { shareChat: shareChat.value, shareArtifact: shareArtifact.value })
  if (!props.shared) {
    createButtonTxt.value = t('tipMessage.copyLinkSuccess').value
  }
}
</script>

<style scope>
.action {
  @apply flex flex-col gap-16px;

  .option {
    @apply flex items-center;

    .desc {
      @apply ml-12px mr-16px;
    }
  }
}
</style>
