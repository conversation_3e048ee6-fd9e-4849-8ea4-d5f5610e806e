// 定义供应商与图标的基础映射关系
// @unocss-include
export const providerBaseIconMap: Record<string, string> = {
  OPENAI: 'i-ri-openai-line',
  ANTHROPIC: 'iconfont icon-Claude',
  MIXTRAL: 'iconfont icon-Mixtral',
  DEEPSEEK: 'i-ju-deepseek',
  GEMINI: 'i-ju-gemini',
  GROK: 'i-ju-grok-logo',
  VISION: 'i-ju-vision',
  QWEN: 'i-ju-qwen-logo',
  Voice: 'iconfont icon-Voice',
  KIMI: 'i-ju-moonshot-logo !text-16px',
}

// 在LLMSelectPanel中使用的图标样式
export const providerPanelIconMap: Record<string, string> = {
  OPENAI: `${providerBaseIconMap.OPENAI} text-18px`,
  ANTHROPIC: `${providerBaseIconMap.ANTHROPIC} mt-1px !text-13px`,
  GEMINI: `${providerBaseIconMap.GEMINI} !text-18px`,
  GROK: `${providerBaseIconMap.GROK} !text-20px`,
  DEEPSEEK: `${providerBaseIconMap.DEEPSEEK} !text-20px`,
  VISION: `${providerBaseIconMap.VISION} !text-20px`,
  QWEN: `${providerBaseIconMap.QWEN} !text-20px`,
  MIXTRAL: `${providerBaseIconMap.MIXTRAL} !text-13px`,
  KIMI: `${providerBaseIconMap.KIMI}`,
  Voice: providerBaseIconMap.Voice,
}

// 在app.ts中使用的图标样式
export const providerAppIconMap: Record<string, string> = {
  OPENAI: providerBaseIconMap.OPENAI,
  ANTHROPIC: `${providerBaseIconMap.ANTHROPIC} !text-13px`,
  MIXTRAL: `${providerBaseIconMap.MIXTRAL} !text-13px`,
  DEEPSEEK: providerBaseIconMap.DEEPSEEK,
  Voice: providerBaseIconMap.Voice,
  GEMINI: providerBaseIconMap.GEMINI,
  VISION: providerBaseIconMap.VISION,
  GROK: providerBaseIconMap.GROK,
  QWEN: providerBaseIconMap.QWEN,
  KIMI: providerBaseIconMap.KIMI,
}

// 通用工具函数，根据provider获取图标类和对应的其他样式，如果没有找到则返回空字符串
export const getProviderIcon = (provider: string, type: 'base' | 'panel' | 'app' = 'base'): string => {
  const providerStr = String(provider)
  let iconMap

  if (type === 'base') {
    iconMap = providerBaseIconMap
  }
  else if (type === 'panel') {
    iconMap = providerPanelIconMap
  }
  else {
    iconMap = providerAppIconMap
  }

  return iconMap[providerStr] || ''
}
