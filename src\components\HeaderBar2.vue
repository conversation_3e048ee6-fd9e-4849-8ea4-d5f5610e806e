<template>
  <div ref="headerContainer" class="flex justify-between transition-cusbezier-300 lt-md:(sticky top-0 z-11)">
    <section class="flex-y-c lt-md:(w-full)">
      <div
        v-if="isPc"
        class="h-[var(--header-bar-height)] w-260px flex-c shrink-0 cursor-pointer bg-primary-600 transition-cusbezier-300"
        :class="{ '!bg-transparent': !expandSidebar }"
        @click="clickLogo"
      >
        <LOGO2 :expand-sidebar="expandSidebar" />
      </div>
      <div class="flex-y-c shrink-0 lt-md:(grid grid-cols-[1fr_auto_1fr] h-60px max-w-full w-full)" :class="{ 'pt-6': isClient }">
        <div
          v-show="!isPc"
          class="h-full flex-y-c pl-20px pr-8px text-16px"
          @click="expandSidebar = !expandSidebar"
        >
          <i class="i-ju-menu-mobile" />
        </div>
        <div class="mt-3px flex-y-c flex-1 pl-39px lt-md:(p-0)">
          <LLMSelect panel-width="100vw" :in-home-top="true" />
          <div v-if="showTool && !isPc && toolsShouldBeDisplayed" class="visual-center">
            <el-popover
              popper-class="w-50! p-0!" trigger="click" placement="bottom-end" :offset="0" :persistent="false"
              :show-arrow="false"
            >
              <template #default>
                <ModelSelect :is-visible="true" />
              </template>
              <template #reference>
                <button class="ml-2">
                  <i class="i-ri-information-line bg-[#A8AFBF] text-[14px]" />
                </button>
              </template>
            </el-popover>
          </div>
        </div>
        <HeaderMore
          v-show="chatViewData.status !== 'READY'"
          class="flex-y-c justify-end"
          button-class="text-16px mr-18px"
          placement="bottom-end"
          @refresh="emits('refresh')"
        />
      </div>
      <div v-if="showTool && isPc && toolsShouldBeDisplayed" class="relative ml-[5px] flex-y-c shrink-0" :class="{ 'pt-6': isClient }">
        <Teleport to="body">
          <ModelSelect v-model:is-visible="showModelSelect" :position="modelSelectBtnPosition" />
        </Teleport>
        <button ref="modelSelectBtn" @click.stop="toggleModelSelect">
          <i class="i-ri-information-line bg-[#A8AFBF] text-[14px]" />
        </button>
      </div>
    </section>

    <section class="flex-y-c">
      <ControlBar
        v-show="['START'].includes(chatViewData.status) && isPc"
        ref="controlBar"
        class="mr-10px"
        :class="{ 'pt-6': isClient }"
        :message-id="messageId"
        :groups="groups"
        :sections="sections"
        :group-id="groupId"
        :content="chatContent?.sectionContent"
        :favorited="isFavorited(messageId)"
        @favorite="favoriteMessage(messageId)"
        @delete="deleteMessage({ id: messageId })"
        @grouping="moveGroup"
        @refresh="emits('refresh')"
      ></ControlBar>

      <div v-if="['START'].includes(chatViewData.status) && isPc" class="mr-39px flex-y-c">
        <ZenButton />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { MODE_TYPE } from '@/enum'
import { useAppStore } from '@/stores/app'
import { useTools } from '@/stores/tools'

const emits = defineEmits(['logo', 'refresh'])
const { headerBarWidth } = storeToRefs(useAppStore())

const headerContainer = ref<HTMLElement>()
const { width: headerContainerWidth } = useElementSize(headerContainer)

watch(headerContainerWidth, (newWidth) => {
  headerBarWidth.value = newWidth
})

const controlBar = ref()
const showModelSelect = ref(false)
const modelSelectBtn = ref()
const modelSelectBtnPosition = ref({
  top: 0,
  left: 0,
})
const { showTool, fetchTools } = useTools()
const { sendDeleteMessage, sendFavoriteMessage, sendMoveGroup } = useAppStore()
const { chatViewData, controlBarSelectTrigger, expandSidebar, isPc, isClient, modelSelectInfo, selectedMode } = storeToRefs(useAppStore())
const { messageId, groups, sections, groupId, chatContent } = toRefs(chatViewData.value)
const route: any = useRoute()
const router = useRouter()

// 当前选择的模型是支持多模态的
const currentSupportsMultimodal = computed(() => {
  const currentModelProvider = modelSelectInfo.value.serviceProviderName.toLowerCase()
  return currentModelProvider === 'openai'
    || currentModelProvider === 'anthropic'
    || currentModelProvider === 'deepseek'
    || selectedMode.value?.type === MODE_TYPE.KIMI_K2_PREVIEW
})

// 暂时应该只有前半段起效果，因为后半段的mode为30表示是旧的Artifacts模型，现在已经下线
const toolsShouldBeDisplayed = computed(() => {
  return currentSupportsMultimodal.value && modelSelectInfo.value.mode !== 30
})

const clickLogo = () => {
  if (['Chat'].includes(route.name)) {
    emits('logo')
  }
  else {
    router.push({
      name: 'Chat',
    })
  }
}
const toggleModelSelect = () => {
  showModelSelect.value = !showModelSelect.value
  const { top, left } = modelSelectBtn.value.getBoundingClientRect()
  modelSelectBtnPosition.value = {
    top: top - 10,
    left: left + 30,
  }
}
const isFavorited = (id: any) => {
  if (!groups.value) {
    return false
  }
  else {
    return !!(groups.value
      ?.find((element: any) => element.starFlag)
      ?.array
      ?.find((element: any) => element.id === id))
  }
}

const favoriteMessage = async (id: number) => {
  sendFavoriteMessage(id)
}

// 原型上没有删除消息的功能，暂时先放这
const deleteMessage = async ({ id }) => {
  sendDeleteMessage(id)
}

const moveGroup = async ({ groupId: _groupId }: any) => {
  groupId.value = _groupId
  sendMoveGroup(_groupId)
}

watch(
  () => controlBarSelectTrigger.value.count,
  () => {
    if (controlBar.value) {
      controlBar.value.select(controlBarSelectTrigger.value.index)
    }
  },
)
watch(
  () => showTool.value,
  (newVal) => {
    if (newVal) {
      fetchTools()
    }
  },
)
</script>

<style lang="scss" scoped>
.visual-center {
  width: 0;
  height: 0;
  display: grid;
  place-content: center start;
  margin-bottom: 2px;
}
</style>
