import sanitizeHtml from 'sanitize-html'
import TurndownService from 'turndown'
import { CelHiveLinkSplitSymbol } from '@/common/constant'
import { requestPlugin } from '@/common/tools'

export const filterHTML = (html: string) => {
  const turndownService = new TurndownService()
  const sanizitedHtml = sanitizeHtml(html, {
    allowedTags: [
      'address',
      'article',
      'aside',
      'footer',
      'header',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'hgroup',
      'main',
      'nav',
      'section',
      'blockquote',
      'dd',
      'div',
      'dl',
      'dt',
      'figcaption',
      'figure',
      'hr',
      'li',
      'main',
      'ol',
      'p',
      'pre',
      'ul',
      'a',
      'abbr',
      'b',
      'bdi',
      'bdo',
      'br',
      'cite',
      'code',
      'data',
      'dfn',
      'em',
      'i',
      'kbd',
      'mark',
      'q',
      'rb',
      'rp',
      'rt',
      'rtc',
      'ruby',
      's',
      'samp',
      'small',
      'span',
      'strong',
      'sub',
      'sup',
      'time',
      'img',
      'u',
      'var',
      'wbr',
      'caption',
      'col',
      'colgroup',
      'table',
      'tbody',
      'td',
      'tfoot',
      'th',
      'thead',
      'tr',
    ],
  })

  const markdown = turndownService.turndown(sanizitedHtml)
  return markdown
}

interface DOMNode {
  index: string
  content: string
  tagName: string
}

export function htmlToIndexedDOM(html: string): DOMNode[] {
  if (!html.includes('<body')) {
    throw new Error('Invalid HTML: Missing <body> tag')
  }

  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  const result: DOMNode[] = []

  function traverse(node: Element, path: number[] = []) {
    if (node.childNodes.length === 0 || (node.childNodes.length === 1 && node.firstChild?.nodeType === Node.TEXT_NODE)) {
      const textContent = node.textContent?.trim()
      const tagName = node.tagName.toLowerCase()
      const filterTagNames = ['script', 'style']
      if (textContent && textContent.length > 0 && !filterTagNames.includes(tagName)) {
        result.push({
          index: path.join('-'),
          content: textContent,
          tagName,
        })
      }
    }
    else {
      Array.from(node.children).forEach((child, index) => {
        traverse(child, [...path, index])
      })
    }
  }

  Array.from(doc.body.children).forEach((child, index) => {
    traverse(child, [index])
  })

  return result
}

export const getDOMByIndex = (index: string): HTMLElement | undefined => {
  const indexArray = index.split('-').map(Number)
  const body = document.body
  let currentNode = body
  for (const i of indexArray) {
    if (currentNode && currentNode.children.length > i) {
      currentNode = currentNode.children[i] as HTMLElement
    }
    else {
      return undefined
    }
  }
  return currentNode
}

export const extractURL = (str: string) => {
  const regex = /https?:\/\/[^\s<>"]+|www\.[^\s<>"]+/gi
  const urls = str.match(regex) || []

  return {
    urls,
    extractURL: str.replace(regex, '').trim(),
  }
}

export const processPrompt = async (prompt: string, isCelHiveLink: boolean) => {
  if (isCelHiveLink) {
    const html = sessionStorage.getItem('celhive-link-html') || ''
    return `${filterHTML(html)}\n\n${CelHiveLinkSplitSymbol}${prompt}`
  }
  else {
    let result = prompt

    const ECP = 'ecp'
    const ECP_SEARCH = 'ecp-search'

    if (result.includes(`/${ECP_SEARCH}`)) {
      try {
        const newPrompt = result.split(`/${ECP_SEARCH}`).filter(Boolean).join('').trim()
        const html = await requestPlugin({ action: ECP_SEARCH, data: { prompt: newPrompt, token: localStorage.token } })
        result = `${filterHTML(html)}\n\n${CelHiveLinkSplitSymbol}${result.replace(`/${ECP_SEARCH}`, '')}`
      }
      catch (error) {
        console.error('ECP plugin error:', error)
        return prompt
      }
    }
    else if (result.includes(`/${ECP}`)) {
      try {
        const { urls } = extractURL(result)
        const html = await requestPlugin({ action: ECP, data: { urls } })
        result = `${filterHTML(html)}\n\n${CelHiveLinkSplitSymbol}${result.replace(`/${ECP}`, '')}`
      }
      catch (error) {
        console.error('ECP plugin error:', error)
        return prompt
      }
    }
    return result
  }
}
