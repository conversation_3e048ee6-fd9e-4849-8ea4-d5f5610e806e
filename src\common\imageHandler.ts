// @unocss-include
import type { Section } from '@/types/index'

interface PreviewImage {
  src: string
  text: string
  prompt: string
}

export interface ImageEventDetail {
  src: string
  text: string
}

// 触发图片预览事件
export function triggerPreviewImage(src: string, text: string) {
  const event = new CustomEvent<ImageEventDetail>('preview-image', {
    detail: { src, text },
  })
  window.dispatchEvent(event)
}

// 为图片添加预览功能
export function addPreviewHandler(img: HTMLImageElement) {
  if (!img.hasAttribute('data-preview-handler')) {
    img.setAttribute('data-preview-handler', 'true')
    img.style.cursor = 'pointer'
    img.addEventListener('click', () => {
      triggerPreviewImage(img.src, img.alt || '')
    })
  }
}

// 通用的blob下载函数
export function downloadBlob(blob: Blob, fileName: string) {
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = URL.createObjectURL(blob)
  link.target = '_blank'
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(link.href)
}

// 下载图片
export async function downloadImage(encodedUrl: string, customFileName?: string) {
  try {
    const url = decodeURIComponent(encodedUrl)
    const response = await fetch(url)
    const blob = await response.blob()
    // 尝试从 URL 中获取原始文件名，移除查询参数
    const urlObj = new URL(url)
    const pathName = urlObj.pathname
    const urlParts = pathName.split('/')
    const originalFileName = urlParts[urlParts.length - 1]

    // 如果 URL 中包含有效的文件名和扩展名，直接下载
    if (originalFileName && originalFileName.includes('.')) {
      downloadBlob(blob, originalFileName)
      return
    }

    // 否则获取 Content-Type 自己确定扩展名
    const contentType = response.headers.get('content-type')

    // 根据 Content-Type 确定扩展名
    const contentTypeToExtension: Record<string, string> = {
      'svg+xml': 'svg',
      'jpeg': 'jpg',
      'png': 'png',
      'gif': 'gif',
      'webp': 'webp',
    }

    let extension = 'png' // 默认扩展名

    // 从 contentTypeToExtension 映射中找到匹配的扩展名
    if (contentType) {
      const matchedType = Object.keys(contentTypeToExtension).find(type => contentType.includes(type))
      if (matchedType) {
        extension = contentTypeToExtension[matchedType]
      }
    }

    // 使用自定义文件名或默认文件名'file'
    const fileName = customFileName ? `${customFileName}.${extension}` : `file.${extension}`
    downloadBlob(blob, fileName)
  }
  catch (error) {
    console.error('下载图片失败:', error)
  }
}

const isDark = useDark()
// 生成图片的 HTML 标记
export function generateImageMarkup(href: string, title: string | null, text: string): string {
  // 通过文件扩展名和路径名判断是否为 SVG
  const isSvg = href.toLowerCase().endsWith('.svg') || href.includes('svg+xml')
  const imgClass = `cursor-pointer ${isSvg && isDark.value ? 'mix-blend-screen' : ''}`

  return `
    <div class="ju-markdown-image-wrapper">
      <div class="ju-markdown-toolbar">
        <div class="ju-markdown-download-btn" onclick="window.downloadImage('${encodeURIComponent(href)}')">
          <i class="i-ju-download-in-markdown"></i>
        </div>
      </div>
      <img 
        src="${href}" 
        alt="${text}" 
        title="${title || ''}" 
        class="${imgClass}"
        onclick="window.previewImage && window.previewImage('${encodeURIComponent(href)}', '${encodeURIComponent(text)}')"
        onerror="this.onerror=null; this.src.startsWith('https://proxy-wechat.alex-lichangnan.workers.dev') ? this.src = this.src : this.src = 'https://proxy-wechat.alex-lichangnan.workers.dev/?url=' + encodeURIComponent(this.src);"
      />
    </div>
  `
}

// 收集预览图片列表
export function collectPreviewImages(
  images: NodeListOf<HTMLImageElement>,
  sections: Section[],
): PreviewImage[] {
  const result: PreviewImage[] = []

  // 获取所有图片和视频元素，并按照它们在 DOM 中的顺序排序
  const mediaElements = [...images, ...document.querySelectorAll('video')]
  const sortedElements = mediaElements.sort((a, b) => {
    // compareDocumentPosition 返回一个位掩码值表示节点间的位置关系
    // 使用 Node.DOCUMENT_POSITION_FOLLOWING (值为4) 判断 b 是否在 a 之后
    // https://developer.mozilla.org/zh-CN/docs/Web/API/Node/compareDocumentPosition
    const position = a.compareDocumentPosition(b)
    // 通过位运算来检查特定的位置关系
    return position & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1
  })

  // 按顺序处理每个媒体元素
  sortedElements.forEach((element) => {
    // 跳过有 data-juchats="true" 属性的元素
    if (element.hasAttribute('data-juchats') && element.getAttribute('data-juchats') === 'true') {
      return
    }

    const isVideo = element instanceof HTMLVideoElement
    // video有时候返回的是source标签，有时候返回的是直接的src，这里兼容处理
    const src = isVideo
      ? (element.src || element.querySelector('source')?.src || '')
      : element.src

    let text = ''

    // 如果是图片，添加点击事件处理器
    if (!isVideo) {
      addPreviewHandler(element as HTMLImageElement)
      text = (element as HTMLImageElement).alt || ''
    }
    else {
      text = (element as HTMLVideoElement).title || ''
    }

    // 找到包含这个媒体元素的消息索引
    const messageIndex = sections.findIndex(section =>
      section.receiving && section.content?.includes(src),
    )

    // 如果找到了消息,获取上一条用户的输入作为prompt
    let prompt = ''
    if (messageIndex > 0) {
      const prevMessage = sections[messageIndex - 1]
      if (!prevMessage.receiving) {
        prompt = prevMessage.question || prevMessage.content || ''
      }
    }
    // 仅当我们找到有效来源时才添加
    if (src) {
      result.push({
        src,
        text,
        prompt,
      })
    }
  })

  return result
}

// 初始化全局图片处理函数
export function initGlobalImageHandlers() {
  if (typeof window !== 'undefined') {
    window.downloadImage = downloadImage
    window.previewImage = triggerPreviewImage
  }
}

// 根据短边计算缩放后的尺寸
// 如果图片是svg，直接复制会非常小，很模糊，所以定义了一个最小尺寸1000，如果短边小于这个尺寸，则进行缩放，长边按比例缩放
export function calculateScaledDimensions(width: number, height: number, minSize = 1000) {
  let scale = 1

  // 如果宽高相等
  if (width === height) {
    if (width < minSize) {
      scale = minSize / width
    }
  }
  // 如果宽度是短边
  else if (width < height) {
    if (width < minSize) {
      scale = minSize / width
    }
  }
  // 如果高度是短边
  else {
    if (height < minSize) {
      scale = minSize / height
    }
  }

  return {
    width: Math.round(width * scale),
    height: Math.round(height * scale),
  }
}

// SVG 转 Base64
export function svgToBase64(svgText: string): string {
  return `data:image/svg+xml;base64,${window.btoa(unescape(encodeURIComponent(svgText)))}`
}

// SVG 转 PNG Blob
export async function svgToPngBlob(
  svgSource: string,
  width: number,
  height: number,
): Promise<Blob> {
  const img = document.createElement('img')
  img.crossOrigin = 'anonymous'
  img.src = svgSource

  await new Promise((resolve, reject) => {
    img.onload = resolve
    img.onerror = reject
  })

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  canvas.width = width
  canvas.height = height

  if (ctx) {
    ctx.fillStyle = '#ffffff' // 设置白色背景
    ctx.fillRect(0, 0, width, height) // 填充整个画布
    ctx.drawImage(img, 0, 0, width, height)
  }

  return new Promise<Blob>((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob)
      }
      else {
        throw new Error('Failed to create blob from canvas')
      }
    }, 'image/png', 1.0)
  })
}

// 复制图片到剪贴板
export async function copyImageToClipboard(blob: Blob): Promise<void> {
  await navigator.clipboard.write([
    new ClipboardItem({ [blob.type]: blob }),
  ])
}

// 图片最大尺寸
export const MAX_IMAGE_DIMENSION = 8000

/**
 * 图片信息对象
 */
export interface ImageDimensions {
  width: number
  height: number
  needsResize: boolean
}

/**
 * 检查图片尺寸
 * @param file 图片文件
 * @returns 图片信息对象，定义详见ImageDimensions
 */
export async function checkImageDimensions(file: File): Promise<ImageDimensions> {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          needsResize: img.width >= MAX_IMAGE_DIMENSION || img.height >= MAX_IMAGE_DIMENSION,
        })
      }
      img.onerror = () => resolve({
        width: 0,
        height: 0,
        needsResize: false,
      })
      img.src = e.target?.result as string
    }

    reader.onerror = () => resolve({
      width: 0,
      height: 0,
      needsResize: false,
    })
    reader.readAsDataURL(file)
  })
}

/**
 * 完全抛弃原来的图片，前端生成一个图片用于上传
 * @param file 图片文件
 * @param dimensions 图片尺寸
 * @returns 重建后的图片文件
 */
export async function rebuildImage(file: File, dimensions?: ImageDimensions): Promise<File> {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader()

      reader.onload = (e) => {
        const img = new Image()

        img.onload = () => {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d', { willReadFrequently: true })

          let finalWidth = img.width
          let finalHeight = img.height

          // MAX_IMAGE_DIMENSION的值是8000
          // 所以重绘的时候，用8000-1=7999来重绘，长边不超过7999，短边按比例缩放
          const MAX_IMAGE_DIMENSION_REBUILD = MAX_IMAGE_DIMENSION - 1
          // 如果需要缩放
          if (dimensions?.needsResize) {
            const aspectRatio = img.width / img.height
            if (img.width >= img.height) {
              // 宽度是最大边
              finalWidth = MAX_IMAGE_DIMENSION_REBUILD
              finalHeight = Math.round(MAX_IMAGE_DIMENSION_REBUILD / aspectRatio)
            }
            else {
              // 高度是最大边
              finalHeight = MAX_IMAGE_DIMENSION_REBUILD
              finalWidth = Math.round(MAX_IMAGE_DIMENSION_REBUILD * aspectRatio)
            }
          }

          canvas.width = finalWidth
          canvas.height = finalHeight

          if (ctx) {
            ctx.fillStyle = '#FFFFFF'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
            ctx.drawImage(img, 0, 0, finalWidth, finalHeight)
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
            ctx.clearRect(0, 0, canvas.width, canvas.height)
            ctx.putImageData(imageData, 0, 0)
          }

          canvas.toBlob((blob) => {
            if (blob) {
              const randomName = `image_${Date.now()}_${Math.random().toString(36).slice(2)}.webp`
              const newFile = new File([blob], randomName, {
                type: 'image/webp',
                lastModified: Date.now(),
              })
              // 下载生成的图片，用于调试
              // const url = URL.createObjectURL(blob)
              // const a = document.createElement('a')
              // a.href = url
              // a.download = randomName
              // document.body.appendChild(a)
              // a.click()
              // document.body.removeChild(a)
              // URL.revokeObjectURL(url)
              resolve(newFile)
            }
            else {
              reject(new Error('图片处理失败'))
            }
          }, 'image/webp', 0.8)
        }

        img.onerror = () => reject(new Error('图片加载失败'))
        img.src = e.target?.result as string
      }

      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsDataURL(file)
    }
    catch (error) {
      reject(error)
    }
  })
}

export async function sharpCompress(file: File): Promise<File> {
  const formData = new FormData()
  formData.append('file', file)
  const res = await fetch(
    `${import.meta.env.VITE_APP_EXPORT_API}/api/app/file/compress`,
    {
      method: 'POST',
      body: formData,
    },
  )
  const blob = await res.blob()
  const randomName = `image_${Date.now()}_${Math.random().toString(36).slice(2)}.webp`
  return new File([blob], randomName, {
    type: 'image/webp',
    lastModified: Date.now(),
  })
}
