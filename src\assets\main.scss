/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable font-family-no-missing-generic-family-keyword */
/* stylelint-disable no-invalid-position-at-import-rule */
*,
*::before,
*::after {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

:root {
  --sb-track-color: #0000;
  --sb-thumb-color: #6668;
  --sb-thumb-hover-color: #666a;
  --sb-size: 9px;
}

/* 默认滚动条样式 */
::-webkit-scrollbar {
  width: var(--sb-size);
  height: var(--sb-size);
}

::-webkit-scrollbar-track {
  @apply bg-transparent rounded-5px dark:rounded-0 dark:bg-#1B1B1B;
}

::-webkit-scrollbar-thumb {
  background: var(--sb-thumb-color);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--sb-thumb-hover-color);
}

/* Mac 风格滚动条 （自动隐藏） */
.mac-scrollbar {
  scrollbar-gutter: stable;

  &::-webkit-scrollbar {
    width: var(--sb-size);
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: var(--sb-track-color);
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--sb-thumb-color);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--sb-thumb-hover-color);
  }

  &.show-scrollbar::-webkit-scrollbar-thumb {
    visibility: visible;
  }
}

@font-face {
  font-family: 'LEDDisplay7';
  src: url('@/assets/fonts/LEDDisplay7/LEDDisplay7.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.led-font {
  font-family: 'LEDDisplay7', sans-serif;
}

.viewer-canvas {
  background: #ffffff90;
  backdrop-filter: blur(10px);
}

@import '@fontsource/alegreya/400.css';
@import '@fontsource/alegreya/500.css';
@import '@fontsource/alegreya/600.css';
@import '@fontsource/alegreya/700.css';
@import '@fontsource/alegreya/800.css';
@import '@fontsource/alegreya/900.css';
@import '@/assets/fonts/HarmonyOS_Sans_SC_Regular/result.css';
@import '@/assets/icons/login-icons/style.css';
@import '@/assets/mode/font/iconfont.css';
@import './styles/side-bar';
@import './styles/markdown';
@import '@fontsource-variable/jetbrains-mono';
@import '@fontsource-variable/jetbrains-mono/wght-italic.css';
@import './styles/highlight-theme';
@import './styles/mermaid-button';

.dark {
  color-scheme: dark;
}

:root,
body {
  background: var(--app-bg);
  overflow: hidden;
  overscroll-behavior: none;
  font-family: var(--font-family);
}

.dark {
  color-scheme: dark;
}

.frame-view {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #fff;

  iframe {
    left: 0;
    right: 0;
    margin: auto;
    height: calc(100% - 80px);
    width: 1000px;
  }
}

.c-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    transition: 0.3s;
    background-color: rgba(black, 0.3);
    border-radius: 4px;
  }
}

.no-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
}

.glide-enter-active,
.glide-leave-active {
  transition: all 0.3s var(--ani-bezier);
}

.glide-enter-from,
.glide-leave-to {
  box-shadow: 0 0 20px 0 #0000;
  transform: translateY(-305px);
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 1s var(--ani-bezier);
}

.slide-enter-from {
  transform: translate3d(0, 100%, 0);
}

.slide-leave-to {
  transform: translate3d(0, 100%, 0);
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s var(--ani-bezier);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.rising-enter-active,
.rising-leave-active {
  transition: all 0.3s var(--ani-bezier);
}

.rising-enter-from {
  transform: translate3d(0, 100%, 0);
}

.rising-leave-to {
  transform: translate3d(0, 100%, 0);
}

.down-enter-active,
.down-leave-active {
  animation: down 0.3s var(--ani-bezier);
}

.down-enter-from,
.down-leave-to {
  animation: back 0.3s var(--ani-bezier) reverse;
}

@keyframes down {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes back {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 0;
  }
}

.side-enter-active,
.side-leave-active {
  transition: transform 0.3s var(--ani-bezier);
}

.side-enter-from,
.side-leave-to {
  transform: translateX(450px);
}

.left-enter-active,
.left-leave-active {
  animation: left 1s ease-in-out;
}

@keyframes rotate {
  0% {
    transform: translateY(-50%) rotate(0);
  }

  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

@keyframes left {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(0);
  }

  60% {
    transform: translateX(-15px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.shrink-enter-active {
  transition: opacity 0.3s var(--ani-bezier);
}

.shrink-enter-from {
  opacity: 0;
}

input,
textarea {
  -webkit-appearance: none;
  background: none;
  outline: none;
  border: none;

  &:disabled {
    color: #a8a8a8;
  }

  /* stylelint-disable-next-line selector-pseudo-element-no-unknown */
  &::input-placeholder {
    /* WebKit browsers */
    color: #a8a8a8;
  }
}

/* stylelint-disable-next-line no-descending-specificity */
textarea {
  resize: none;
}

.rotate {
  transform-origin: center;
  animation: rotateAnimation 2s infinite var(--ani-bezier);
}

.md-editor,
.md-editor-preview-wrapper,
.md-editor-preview {
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
  line-height: 20px !important;
  font-size: 14px !important;
}

.default-theme p {
  padding: 0 !important;
}

.default-theme pre::before {
  background-image: none !important;
}

/* stylelint-disable-next-line keyframes-name-pattern */
@keyframes rotateAnimation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// .el-loading-spinner {
//   filter: brightness(0);filter
// }

.custom-loading {
  --el-color-primary: var(--black);
}

.el-notification {
  margin-top: 94px !important;
  box-shadow: 0 5px 20px 0 #00000012 !important;
  border-radius: 5px !important;
  border: none !important;
  width: auto !important;
  padding-right: 40px !important;
  padding-left: 5px !important;

  &__icon {
    display: none !important;
  }

  &__title {
    font-weight: normal !important;
    font-size: 14px !important;
  }

  &__closeBtn {
    color: #cbd5e1 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
  }
}

table,
th,
td {
  border: 1px solid #ccc;
}

td,
th {
  padding: 10px;
}

.flash-animation {
  animation: opacityCycle 1s infinite;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.animationFadeIn {
  animation: forwards animationFadeIn 3s;
}

.animationFadeInFast {
  animation: forwards animationFadeIn 0.4s var(--ani-bezier);
}

/* stylelint-disable-next-line keyframes-name-pattern */
@keyframes animationFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-2px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* stylelint-disable-next-line keyframes-name-pattern */
@keyframes opacityCycle {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.el-popper {
  // border-radius: 5px !important;border-radius

  // box-shadow: 0 5px 20px 0 #00000012 !important;box-shadow
  border: none !important;
}

.el-popconfirm__action {
  display: flex;
  flex-direction: row-reverse;

  button {
    background: transparent !important;
    border: none !important;
    font-size: 12px !important;

    &:last-child {
      color: #f00 !important;
    }

    &:first-child {
      color: #a5befa !important;
    }
  }
}

a {
  cursor: pointer;
  color: #5e82d7;
}

.notification__upgrade {
  color: #f65134;
  font-weight: 600;
  padding-left: 10px;
  cursor: pointer;
}

.notification__expire {
  color: #f65134;
  font-weight: 600;
  padding-left: 20px;
  cursor: pointer;
}

.notification__flag {
  color: #f00;
  font-size: 20px;
  line-height: 20px;
  padding-right: 10px;
  font-family: Damion;
  display: inline-block;
  transform: translateY(1px);
}

.mobile-menu {
  width: 150px;
  min-width: 150px;

  .menu-item {
    min-width: 100%;
    width: 100%;

    span {
      @apply truncate  w-85px;
    }
  }
}

@keyframes light-to-dark {
  from {
    clip-path: polygon(0 0,
    0 0,
    calc(tan(8deg) * -100vh) 100%,
    calc(tan(8deg) * -100vh) 100%);
  }

  to {
    clip-path: polygon(0 0,
    calc((tan(8deg) * 100vh) + 100%) 0,
    100% 100%,
    calc(tan(8deg) * -100vh) 100%);
  }
}

@keyframes dark-to-light {
  from {
    clip-path: polygon(calc((tan(8deg) * 100vh) + 100%) 0,
    calc((tan(8deg) * 100vh) + 100%) 0,
    100% 100%,
    100% 100%);
  }

  to {
    clip-path: polygon(0 0,
    calc((tan(8deg) * 100vh) + 100%) 0,
    100% 100%,
    calc(tan(8deg) * -100vh) 100%);
  }
}

:root::view-transition-group(root) {
  animation-duration: var(--theme-transition-duration, 200ms);
}

:root::view-transition-new(root),
:root::view-transition-old(root) {
  mix-blend-mode: normal;
}

:root::view-transition-old(root),
:root[theme-mode='dark']::view-transition-old(root) {
  animation: none;
}

:root::view-transition-new(root) {
  animation-name: dark-to-light;
}

:root[theme-mode='dark']::view-transition-new(root) {
  animation-name: light-to-dark;
}

button:focus {
  outline: none;
}

.transparent-grid-bg {
  background-image: repeating-conic-gradient(#eee 0 25%, #fff 0 50%);
  background-size: 16px 16px;
}

// 反色
.invert-color {
  filter: invert(1) hue-rotate(180deg);
}

.scrollbar-none {
  &::-webkit-scrollbar {
    display: none;
  }
}

.user-shark-text {
  -webkit-text-fill-color: transparent;
  background: linear-gradient(
    110deg,
    rgba(255, 255, 255, 0%) 0%,
    rgba(255, 255, 255, 60%) 40%,
    rgba(255, 255, 255, 60%) 60%,
    rgba(255, 255, 255, 0%) 100%
  ) -100% / 50% no-repeat currentcolor;
  -webkit-background-clip: text;

  &:hover {
    animation: user-shark-text 2s;
  }
}

@keyframes user-shark-text {
  to {
    background-position: 200%;
  }
}

.break-word {
  word-break: break-word;
  overflow-wrap: break-word;
}

.no-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
}

.artifacts-card {
  @apply size-40px flex-c rounded-5px bg-[#000000] light:shadow-[0_5px_5px_0_#36445738];
}

.card-button {
  @apply w-280px flex cursor-pointer rounded-10px select-none bg-[var(--artifacts-card-bg)] p-10px text-[var(--artifacts-card-text)] transition-all light:shadow-[0_0_40px_0_#0000000f] dark:hover:shadow-[0_0_10px_0_#0000001a] light:hover:shadow-[0_0_40px_0_#0000001a];
}

@keyframes rainbow-border {
  to {
    transform: translateX(-50%)
  }
}

.cus-translateX-enter-active,
.cus-translateX-leave-active {
  transition: all 0.3s var(--ani-bezier);
}

.cus-translateX-enter-from,
.cus-translateX-leave-to {
  opacity: 0;
  transform: translateX(-8px);
}

.cus-translateX-move {
  transition: all 0.3s var(--ani-bezier);
}

.cus-translateX-leave-active {
  position: absolute;
}

.juchats-monaco-main {
  .monaco-editor {
    .find-widget {
      .codicon-widget-close {
        width: 22px;
        height: 22px;
      }
    }
  }
}

.el-popper.is-customized {
  padding: 6px 12px;
  background: var(--p3);
  color: var(--white);
}

.el-popper.is-customized .el-popper__arrow::before {
  background: var(--p3);
  color: var(--white);
  right: 0;
}

.chat_item_margin {
  --margin-left-base: 60px;
}

@media (width > 1536px) {
  .chat_item_margin {
    margin-left: calc(var(--margin-left-base) - 0px) !important;
  }
}

@media (1280px < width <= 1536px) {
  .chat_item_margin {
    margin-left: calc(var(--margin-left-base) - 10px) !important;
  }
}

@media (1024px < width <= 1280px) {
  .chat_item_margin {
    margin-left: calc(var(--margin-left-base) - 20px) !important;
  }
}

@media (768px < width <= 1024px) {
  .chat_item_margin {
    margin-left: calc(var(--margin-left-base) - 10px) !important;
  }
}

@media (width <= 768px) {
  .chat_item_margin {
    margin-left: 10px !important;
  }
}
